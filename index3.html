<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CABLETEST - Professional Cable Testing Solutions</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
        }

        /* Header */
        .header {
            background: #fff;
            padding: 10px 0;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }

        .nav-container {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
        }

        .logo {
            font-size: 24px;
            font-weight: bold;
            color: #333;
        }

        .nav-menu {
            display: flex;
            list-style: none;
            gap: 30px;
        }

        .nav-menu > li {
            position: relative;
        }

        .nav-menu a {
            text-decoration: none;
            color: #333;
            font-weight: 500;
            padding: 10px 0;
            display: block;
            transition: color 0.3s ease;
        }

        .nav-menu a:hover {
            color: #ff6b35;
        }

        /* Dropdown Menu Styles */
        .dropdown {
            position: absolute;
            top: 100%;
            left: 0;
            background: white;
            min-width: 200px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            border-radius: 8px;
            opacity: 0;
            visibility: hidden;
            transform: translateY(-10px);
            transition: all 0.3s ease;
            z-index: 1000;
            padding: 10px 0;
        }

        .nav-menu li:hover .dropdown {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }

        .dropdown li {
            list-style: none;
        }

        .dropdown a {
            padding: 12px 20px;
            color: #333;
            font-weight: 400;
            border-bottom: 1px solid #f0f0f0;
        }

        .dropdown a:hover {
            background: #f8f9fa;
            color: #ff6b35;
        }

        .dropdown li:last-child a {
            border-bottom: none;
        }

        /* Arrow indicator for dropdown */
        .has-dropdown > a::after {
            content: '▼';
            font-size: 10px;
            margin-left: 8px;
            transition: transform 0.3s ease;
        }

        .has-dropdown:hover > a::after {
            transform: rotate(180deg);
        }

        /* Hero Section */
        .hero {
            background: linear-gradient(135deg, #87CEEB 0%, #98FB98 100%);
            padding: 80px 0;
            position: relative;
            overflow: hidden;
        }

        .hero-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .hero-content {
            flex: 1;
            max-width: 500px;
        }

        .hero-badge {
            background: #ff6b35;
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            display: inline-block;
            margin-bottom: 20px;
        }

        .hero-title {
            font-size: 48px;
            font-weight: bold;
            color: #333;
            margin-bottom: 20px;
            line-height: 1.2;
        }

        .hero-subtitle {
            font-size: 18px;
            color: #666;
            margin-bottom: 30px;
        }

        .cta-button {
            background: #ff6b35;
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 25px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
        }

        .cta-button:hover {
            background: #e55a2b;
        }

        .hero-image {
            flex: 1;
            text-align: center;
        }

        .hero-image img {
            max-width: 100%;
            height: auto;
        }

        /* Stats Section */
        .stats {
            background: #f8f9fa;
            padding: 60px 0;
        }

        .stats-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 40px;
            text-align: center;
        }

        .stat-item h3 {
            font-size: 36px;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }

        .stat-item p {
            color: #666;
            font-size: 16px;
        }

        /* Products Section */
        .products {
            padding: 80px 0;
        }

        .products-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        .section-title {
            text-align: center;
            font-size: 36px;
            font-weight: bold;
            color: #333;
            margin-bottom: 50px;
        }

        .products-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 30px;
        }

        .product-card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }

        .product-card:hover {
            transform: translateY(-5px);
        }

        .product-card img {
            width: 100%;
            height: 150px;
            object-fit: cover;
            border-radius: 8px;
            margin-bottom: 15px;
        }

        .product-card h4 {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }

        .product-card p {
            color: #666;
            font-size: 14px;
            margin-bottom: 15px;
        }

        .product-price {
            font-size: 20px;
            font-weight: bold;
            color: #ff6b35;
        }

        /* Gallery Section */
        .gallery {
            background: #f8f9fa;
            padding: 80px 0;
        }

        .gallery-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        .gallery-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 20px;
            margin-bottom: 40px;
        }

        .gallery-item {
            position: relative;
            border-radius: 10px;
            overflow: hidden;
        }

        .gallery-item img {
            width: 100%;
            height: 200px;
            object-fit: cover;
        }

        .gallery-overlay {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background: linear-gradient(transparent, rgba(0,0,0,0.7));
            color: white;
            padding: 20px;
        }

        /* 8K Series Section */
        .series-8k {
            background: #333;
            color: white;
            padding: 80px 0;
        }

        .series-8k-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            align-items: center;
            gap: 50px;
        }

        .series-8k-content {
            flex: 1;
        }

        .series-8k-title {
            font-size: 48px;
            font-weight: bold;
            margin-bottom: 20px;
        }

        .series-8k-subtitle {
            font-size: 18px;
            margin-bottom: 30px;
            opacity: 0.9;
        }

        .series-8k-image {
            flex: 1;
        }

        .series-8k-image img {
            width: 100%;
            height: auto;
        }

        /* Featured Section */
        .featured {
            padding: 80px 0;
        }

        .featured-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            text-align: center;
        }

        .featured-logos {
            display: flex;
            justify-content: space-around;
            align-items: center;
            margin-top: 40px;
            opacity: 0.6;
        }

        .featured-logos img {
            height: 40px;
            filter: grayscale(100%);
        }

        /* Social Media Section */
        .social-media {
            background: #f8f9fa;
            padding: 80px 0;
        }

        .social-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        .social-grid {
            display: grid;
            grid-template-columns: repeat(5, 1fr);
            gap: 20px;
            margin-top: 40px;
        }

        .social-item {
            aspect-ratio: 1;
            border-radius: 10px;
            overflow: hidden;
        }

        .social-item img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        /* Programs Section */
        .programs {
            padding: 80px 0;
        }

        .programs-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        .programs-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 40px;
            margin-top: 40px;
        }

        .program-card {
            text-align: center;
            padding: 40px 20px;
            border-radius: 10px;
            background: white;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .program-icon {
            width: 80px;
            height: 80px;
            margin: 0 auto 20px;
            background: #ff6b35;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 32px;
            color: white;
        }

        .program-card h4 {
            font-size: 20px;
            margin-bottom: 15px;
            color: #333;
        }

        .program-card p {
            color: #666;
            line-height: 1.6;
        }

        /* Blog Section */
        .blog {
            background: #f8f9fa;
            padding: 80px 0;
        }

        .blog-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        .blog-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 30px;
            margin-top: 40px;
        }

        .blog-card {
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .blog-card img {
            width: 100%;
            height: 200px;
            object-fit: cover;
        }

        .blog-content {
            padding: 20px;
        }

        .blog-date {
            color: #ff6b35;
            font-size: 14px;
            margin-bottom: 10px;
        }

        .blog-title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }

        .blog-excerpt {
            color: #666;
            font-size: 14px;
            line-height: 1.6;
        }

        /* Newsletter Section */
        .newsletter {
            background: #333;
            color: white;
            padding: 80px 0;
        }

        .newsletter-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            text-align: center;
        }

        .newsletter-title {
            font-size: 36px;
            font-weight: bold;
            margin-bottom: 20px;
        }

        .newsletter-subtitle {
            font-size: 18px;
            margin-bottom: 40px;
            opacity: 0.9;
        }

        .newsletter-form {
            display: flex;
            justify-content: center;
            gap: 20px;
            max-width: 500px;
            margin: 0 auto;
        }

        .newsletter-input {
            flex: 1;
            padding: 15px 20px;
            border: none;
            border-radius: 25px;
            font-size: 16px;
        }

        .newsletter-button {
            background: #ff6b35;
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 25px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
        }

        .newsletter-button:hover {
            background: #e55a2b;
        }

        /* Footer */
        .footer {
            background: #222;
            color: white;
            padding: 60px 0 20px;
        }

        .footer-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        .footer-content {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 40px;
            margin-bottom: 40px;
        }

        .footer-section h4 {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 20px;
            color: #ff6b35;
        }

        .footer-section ul {
            list-style: none;
        }

        .footer-section ul li {
            margin-bottom: 10px;
        }

        .footer-section ul li a {
            color: #ccc;
            text-decoration: none;
            font-size: 14px;
        }

        .footer-section ul li a:hover {
            color: #ff6b35;
        }

        .footer-bottom {
            border-top: 1px solid #444;
            padding-top: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .footer-bottom p {
            color: #ccc;
            font-size: 14px;
        }

        .social-links {
            display: flex;
            gap: 15px;
        }

        .social-links a {
            color: #ccc;
            font-size: 20px;
            text-decoration: none;
        }

        .social-links a:hover {
            color: #ff6b35;
        }

        /* Mobile dropdown styles */
        @media (max-width: 768px) {
            .nav-menu {
                flex-direction: column;
                gap: 0;
            }

            .dropdown {
                position: static;
                opacity: 0;
                visibility: hidden;
                transform: none;
                box-shadow: none;
                background: #f8f9fa;
                border-radius: 0;
                max-height: 0;
                overflow: hidden;
                transition: all 0.3s ease;
            }

            .has-dropdown.active .dropdown {
                opacity: 1;
                visibility: visible;
                max-height: 300px;
                padding: 10px 0;
            }

            .dropdown a {
                padding: 10px 40px;
                font-size: 14px;
            }
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .hero-container {
                flex-direction: column;
                text-align: center;
            }

            .hero-title {
                font-size: 36px;
            }

            .stats-container {
                grid-template-columns: repeat(2, 1fr);
            }

            .products-grid {
                grid-template-columns: repeat(2, 1fr);
            }

            .gallery-grid {
                grid-template-columns: 1fr;
            }

            .series-8k-container {
                flex-direction: column;
            }

            .programs-grid {
                grid-template-columns: 1fr;
            }

            .blog-grid {
                grid-template-columns: 1fr;
            }

            .footer-content {
                grid-template-columns: repeat(2, 1fr);
            }

            .newsletter-form {
                flex-direction: column;
            }
        }

        @media (max-width: 480px) {
            .nav-menu {
                display: none;
            }

            .stats-container {
                grid-template-columns: 1fr;
            }

            .products-grid {
                grid-template-columns: 1fr;
            }

            .footer-content {
                grid-template-columns: 1fr;
            }

            .footer-bottom {
                flex-direction: column;
                gap: 20px;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="nav-container">
            <div class="logo">CABLETEST</div>
            <nav>
                <ul class="nav-menu">
                    <li><a href="#home">HOME</a></li>
                    <li class="has-dropdown">
                        <a href="#products">PRODUCTS</a>
                        <ul class="dropdown">
                            <li><a href="#cable-testers">Cable Testers</a></li>
                            <li><a href="#network-analyzers">Network Analyzers</a></li>
                            <li><a href="#fiber-optic">Fiber Optic Tools</a></li>
                            <li><a href="#multimeters">Digital Multimeters</a></li>
                            <li><a href="#accessories">Accessories</a></li>
                            <li><a href="#8k-series">8K Series</a></li>
                        </ul>
                    </li>
                    <li class="has-dropdown">
                        <a href="#solutions">SOLUTIONS</a>
                        <ul class="dropdown">
                            <li><a href="#enterprise">Enterprise Solutions</a></li>
                            <li><a href="#datacenter">Data Center Testing</a></li>
                            <li><a href="#telecom">Telecom Infrastructure</a></li>
                            <li><a href="#industrial">Industrial Networks</a></li>
                            <li><a href="#residential">Residential Wiring</a></li>
                        </ul>
                    </li>
                    <li class="has-dropdown">
                        <a href="#support">SUPPORT</a>
                        <ul class="dropdown">
                            <li><a href="#documentation">Documentation</a></li>
                            <li><a href="#downloads">Software Downloads</a></li>
                            <li><a href="#training">Training & Certification</a></li>
                            <li><a href="#warranty">Warranty Service</a></li>
                            <li><a href="#technical-support">Technical Support</a></li>
                            <li><a href="#repair-service">Repair Service</a></li>
                        </ul>
                    </li>
                    <li class="has-dropdown">
                        <a href="#about">ABOUT US</a>
                        <ul class="dropdown">
                            <li><a href="#company">Our Company</a></li>
                            <li><a href="#team">Our Team</a></li>
                            <li><a href="#history">Company History</a></li>
                            <li><a href="#careers">Careers</a></li>
                            <li><a href="#news">News & Press</a></li>
                        </ul>
                    </li>
                    <li class="has-dropdown">
                        <a href="#contact">CONTACT</a>
                        <ul class="dropdown">
                            <li><a href="#sales">Sales Inquiry</a></li>
                            <li><a href="#support-contact">Technical Support</a></li>
                            <li><a href="#distributors">Find Distributors</a></li>
                            <li><a href="#locations">Office Locations</a></li>
                        </ul>
                    </li>
                </ul>
            </nav>
        </div>
    </header>

    <!-- Hero Section -->
    <section class="hero">
        <div class="hero-container">
            <div class="hero-content">
                <div class="hero-badge">March 1st - 31st</div>
                <h1 class="hero-title">Up to 20% Off</h1>
                <p class="hero-subtitle">Professional cable testing solutions for all your networking needs. Get the best deals this month!</p>
                <a href="#products" class="cta-button">Shop Now</a>
            </div>
            <div class="hero-image">
                <img src="https://placehold.co/500x400" alt="Cable Testing Equipment">
            </div>
        </div>
    </section>

    <!-- Stats Section -->
    <section class="stats">
        <div class="stats-container">
            <div class="stat-item">
                <h3>1000+</h3>
                <p>Happy Customers</p>
            </div>
            <div class="stat-item">
                <h3>50+</h3>
                <p>Products</p>
            </div>
            <div class="stat-item">
                <h3>24/7</h3>
                <p>Support</p>
            </div>
            <div class="stat-item">
                <h3>99%</h3>
                <p>Satisfaction</p>
            </div>
        </div>
    </section>

    <!-- Products Section -->
    <section class="products">
        <div class="products-container">
            <h2 class="section-title">Hot Sale</h2>
            <div class="products-grid">
                <div class="product-card">
                    <img src="https://placehold.co/250x150" alt="Cable Tester">
                    <h4>Professional Cable Tester</h4>
                    <p>High-precision cable testing device for network diagnostics</p>
                    <div class="product-price">$299.99</div>
                </div>
                <div class="product-card">
                    <img src="https://placehold.co/250x150" alt="Network Analyzer">
                    <h4>Network Analyzer Pro</h4>
                    <p>Advanced network analysis and troubleshooting tool</p>
                    <div class="product-price">$599.99</div>
                </div>
                <div class="product-card">
                    <img src="https://placehold.co/250x150" alt="Fiber Tester">
                    <h4>Fiber Optic Tester</h4>
                    <p>Comprehensive fiber optic cable testing solution</p>
                    <div class="product-price">$899.99</div>
                </div>
                <div class="product-card">
                    <img src="https://placehold.co/250x150" alt="Digital Multimeter">
                    <h4>Digital Multimeter</h4>
                    <p>Precision digital multimeter for electrical testing</p>
                    <div class="product-price">$149.99</div>
                </div>
            </div>
        </div>
    </section>

    <!-- Gallery Section -->
    <section class="gallery">
        <div class="gallery-container">
            <h2 class="section-title">Latest Case Studies</h2>
            <div class="gallery-grid">
                <div class="gallery-item">
                    <img src="https://placehold.co/400x200" alt="Network Setup">
                    <div class="gallery-overlay">
                        <h4>Enterprise Network Setup</h4>
                    </div>
                </div>
                <div class="gallery-item">
                    <img src="https://placehold.co/400x200" alt="Cable Testing">
                    <div class="gallery-overlay">
                        <h4>Cable Testing Project</h4>
                    </div>
                </div>
                <div class="gallery-item">
                    <img src="https://placehold.co/400x200" alt="Fiber Installation">
                    <div class="gallery-overlay">
                        <h4>Fiber Optic Installation</h4>
                    </div>
                </div>
            </div>
            <div style="text-align: center;">
                <span style="background: #ff6b35; color: white; padding: 10px 20px; border-radius: 20px; font-size: 18px; font-weight: bold;">5 21</span>
            </div>
        </div>
    </section>

    <!-- 8K Series Section -->
    <section class="series-8k">
        <div class="series-8k-container">
            <div class="series-8k-content">
                <h2 class="series-8k-title">8K Series</h2>
                <p class="series-8k-subtitle">Next-generation cable testing technology with 8K resolution display and advanced diagnostics capabilities.</p>
                <a href="#" class="cta-button">Learn More</a>
            </div>
            <div class="series-8k-image">
                <img src="https://placehold.co/500x300" alt="8K Series Device">
            </div>
        </div>
    </section>

    <!-- Featured Section -->
    <section class="featured">
        <div class="featured-container">
            <h2 class="section-title">Featured On</h2>
            <div class="featured-logos">
                <img src="https://placehold.co/120x40" alt="TechNews">
                <img src="https://placehold.co/120x40" alt="Industry Today">
                <img src="https://placehold.co/120x40" alt="Cable World">
                <img src="https://placehold.co/120x40" alt="Network Pro">
            </div>
        </div>
    </section>

    <!-- Social Media Section -->
    <section class="social-media">
        <div class="social-container">
            <h2 class="section-title">Share on Social Media</h2>
            <div class="social-grid">
                <div class="social-item">
                    <img src="https://placehold.co/200x200" alt="Facebook">
                </div>
                <div class="social-item">
                    <img src="https://placehold.co/200x200" alt="Twitter">
                </div>
                <div class="social-item">
                    <img src="https://placehold.co/200x200" alt="LinkedIn">
                </div>
                <div class="social-item">
                    <img src="https://placehold.co/200x200" alt="Instagram">
                </div>
                <div class="social-item">
                    <img src="https://placehold.co/200x200" alt="YouTube">
                </div>
            </div>
        </div>
    </section>

    <!-- Programs Section -->
    <section class="programs">
        <div class="programs-container">
            <h2 class="section-title">CABLETEST Programs</h2>
            <div class="programs-grid">
                <div class="program-card">
                    <div class="program-icon">📊</div>
                    <h4>Distribution</h4>
                    <p>Comprehensive distribution network for global reach and local support.</p>
                </div>
                <div class="program-card">
                    <div class="program-icon">🏆</div>
                    <h4>Warranties</h4>
                    <p>Extended warranty programs to protect your investment in quality equipment.</p>
                </div>
                <div class="program-card">
                    <div class="program-icon">🎓</div>
                    <h4>Education</h4>
                    <p>Training programs and certification courses for technical professionals.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Blog Section -->
    <section class="blog">
        <div class="blog-container">
            <h2 class="section-title">Blogs and News</h2>
            <div class="blog-grid">
                <div class="blog-card">
                    <img src="https://placehold.co/350x200" alt="Blog Post 1">
                    <div class="blog-content">
                        <div class="blog-date">March 15, 2024</div>
                        <h4 class="blog-title">The Future of Cable Testing Technology</h4>
                        <p class="blog-excerpt">Exploring the latest innovations in cable testing and network diagnostics...</p>
                    </div>
                </div>
                <div class="blog-card">
                    <img src="https://placehold.co/350x200" alt="Blog Post 2">
                    <div class="blog-content">
                        <div class="blog-date">March 10, 2024</div>
                        <h4 class="blog-title">Best Practices for Network Installation</h4>
                        <p class="blog-excerpt">Professional tips and techniques for successful network installations...</p>
                    </div>
                </div>
                <div class="blog-card">
                    <img src="https://placehold.co/350x200" alt="Blog Post 3">
                    <div class="blog-content">
                        <div class="blog-date">March 5, 2024</div>
                        <h4 class="blog-title">Troubleshooting Common Cable Issues</h4>
                        <p class="blog-excerpt">A comprehensive guide to identifying and resolving cable problems...</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Newsletter Section -->
    <section class="newsletter">
        <div class="newsletter-container">
            <h2 class="newsletter-title">Subscribe now for a special discount</h2>
            <p class="newsletter-subtitle">Get the latest updates and exclusive offers delivered to your inbox</p>
            <form class="newsletter-form">
                <input type="email" class="newsletter-input" placeholder="Enter your email address" required>
                <button type="submit" class="newsletter-button">Subscribe</button>
            </form>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="footer-container">
            <div class="footer-content">
                <div class="footer-section">
                    <h4>Company</h4>
                    <ul>
                        <li><a href="#">About Us</a></li>
                        <li><a href="#">Our Team</a></li>
                        <li><a href="#">Careers</a></li>
                        <li><a href="#">Press</a></li>
                        <li><a href="#">Contact</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>Products</h4>
                    <ul>
                        <li><a href="#">Cable Testers</a></li>
                        <li><a href="#">Network Analyzers</a></li>
                        <li><a href="#">Fiber Optic Tools</a></li>
                        <li><a href="#">Multimeters</a></li>
                        <li><a href="#">Accessories</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>Support</h4>
                    <ul>
                        <li><a href="#">Documentation</a></li>
                        <li><a href="#">Downloads</a></li>
                        <li><a href="#">Training</a></li>
                        <li><a href="#">Warranty</a></li>
                        <li><a href="#">Service</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>Resources</h4>
                    <ul>
                        <li><a href="#">Blog</a></li>
                        <li><a href="#">Case Studies</a></li>
                        <li><a href="#">White Papers</a></li>
                        <li><a href="#">Webinars</a></li>
                        <li><a href="#">Events</a></li>
                    </ul>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 CABLETEST. All rights reserved.</p>
                <div class="social-links">
                    <a href="#">📘</a>
                    <a href="#">🐦</a>
                    <a href="#">💼</a>
                    <a href="#">📷</a>
                    <a href="#">📺</a>
                </div>
            </div>
        </div>
    </footer>

    <script>
        // Newsletter form submission
        document.querySelector('.newsletter-form').addEventListener('submit', function(e) {
            e.preventDefault();
            const email = document.querySelector('.newsletter-input').value;
            if (email) {
                alert('Thank you for subscribing! We\'ll send you updates at ' + email);
                document.querySelector('.newsletter-input').value = '';
            }
        });

        // Smooth scrolling for navigation links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth'
                    });
                }
            });
        });

        // Add scroll effect to header
        window.addEventListener('scroll', function() {
            const header = document.querySelector('.header');
            if (window.scrollY > 100) {
                header.style.background = 'rgba(255, 255, 255, 0.95)';
                header.style.backdropFilter = 'blur(10px)';
            } else {
                header.style.background = '#fff';
                header.style.backdropFilter = 'none';
            }
        });

        // Enhanced dropdown menu functionality
        document.addEventListener('DOMContentLoaded', function() {
            const dropdownItems = document.querySelectorAll('.has-dropdown');

            // Handle click events for mobile devices
            dropdownItems.forEach(item => {
                const link = item.querySelector('a');
                const dropdown = item.querySelector('.dropdown');

                // Prevent default click behavior on parent links with dropdowns
                link.addEventListener('click', function(e) {
                    if (window.innerWidth <= 768) {
                        e.preventDefault();

                        // Close other dropdowns
                        dropdownItems.forEach(otherItem => {
                            if (otherItem !== item) {
                                otherItem.classList.remove('active');
                            }
                        });

                        // Toggle current dropdown
                        item.classList.toggle('active');
                    }
                });
            });

            // Close dropdowns when clicking outside
            document.addEventListener('click', function(e) {
                if (!e.target.closest('.nav-menu')) {
                    dropdownItems.forEach(item => {
                        item.classList.remove('active');
                    });
                }
            });

            // Handle window resize
            window.addEventListener('resize', function() {
                if (window.innerWidth > 768) {
                    dropdownItems.forEach(item => {
                        item.classList.remove('active');
                    });
                }
            });
        });
    </script>
</body>
</html>
