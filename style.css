body {
    font-family: Arial, sans-serif;
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    margin: 0;
    background-color: #f0f0f0;
}

.container {
    text-align: center;
}

h1 {
    color: #333;
}

.game-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding: 0 20px;
}

.current-player {
    font-size: 18px;
    font-weight: bold;
}

#restart-btn {
    padding: 10px 20px;
    font-size: 16px;
    background-color: #4CAF50;
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
}

#restart-btn:hover {
    background-color: #45a049;
}

.chessboard {
    display: inline-block;
    background-color: #DDB88C;
    border: 2px solid #333;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);
    line-height: 0; /* 确保单元格正确对齐 */
    overflow: hidden; /* 确保内容不溢出 */
    width: 400px; /* 10列 × 40px = 400px，强制换行 */
}

.cell {
    width: 40px;
    height: 40px;
    position: relative;
    display: inline-block;
    vertical-align: top; /* 确保单元格顶部对齐 */
}

.cell:before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        linear-gradient(90deg, rgba(0, 0, 0, 0.2) 1px, transparent 0),
        linear-gradient(rgba(0, 0, 0, 0.2) 1px, transparent 0);
    background-size: 40px 40px;
}

.cell:hover {
    background-color: rgba(255, 255, 0, 0.3);
}

.black-piece, .white-piece {
    position: absolute;
    width: 34px;
    height: 34px;
    border-radius: 50%;
    top: 3px;
    left: 3px;
    z-index: 10;
    cursor: pointer;
}

.black-piece {
    background: radial-gradient(circle at 30% 30%, #666, #000);
    box-shadow: 1px 1px 3px rgba(0, 0, 0, 0.5);
}

.white-piece {
    background: radial-gradient(circle at 30% 30%, #fff, #ddd);
    box-shadow: 1px 1px 3px rgba(0, 0, 0, 0.5);
}

.game-status {
    margin-top: 20px;
    font-size: 24px;
    font-weight: bold;
    min-height: 30px;
}