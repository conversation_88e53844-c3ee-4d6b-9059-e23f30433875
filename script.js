// 游戏状态变量
let currentPlayer = 'black'; // 当前玩家，black或white
let gameBoard = []; // 存储棋盘状态的二维数组
let gameOver = false; // 游戏是否结束
const BOARD_SIZE = 10; // 棋盘大小

// 初始化游戏
function initGame() {
    // 初始化棋盘状态
    gameBoard = Array(BOARD_SIZE).fill().map(() => Array(BOARD_SIZE).fill(null));
    gameOver = false;
    
    // 清空棋盘显示
    const chessboard = document.getElementById('chessboard');
    chessboard.innerHTML = '';
    
    // 清空游戏状态显示
    document.getElementById('game-status').textContent = '';
    
    // 更新当前玩家显示
    updateCurrentPlayerDisplay();
    
    // 创建棋盘
    createChessboard();
}

// 创建棋盘
function createChessboard() {
    const chessboard = document.getElementById('chessboard');
    
    // 创建10x10的棋盘
    for (let row = 0; row < BOARD_SIZE; row++) {
        for (let col = 0; col < BOARD_SIZE; col++) {
            const cell = document.createElement('div');
            cell.className = 'cell';
            cell.dataset.row = row;
            cell.dataset.col = col;
            
            // 添加点击事件监听器
            cell.addEventListener('click', () => handleCellClick(row, col));
            
            chessboard.appendChild(cell);
        }
    }
    
    // 棋盘创建完成
}

// 处理单元格点击事件
function handleCellClick(row, col) {
    // 如果游戏结束或该位置已有棋子，或当前不是黑棋回合，则不处理
    if (gameOver || gameBoard[row][col] !== null || currentPlayer !== 'black') {
        return;
    }
    
    // 在棋盘上放置黑棋
    placePiece(row, col);
    
    // 检查是否获胜
    if (checkWin(row, col)) {
        gameOver = true;
        document.getElementById('game-status').textContent = '黑子获胜！';
        return;
    }
    
    // 切换到白棋（电脑）回合
    currentPlayer = 'white';
    updateCurrentPlayerDisplay();
    
    // 电脑下棋
    setTimeout(() => {
        computerMove();
    }, 500); // 延迟500毫秒，让人看到电脑思考的过程
}

// 电脑AI下棋
function computerMove() {
    if (gameOver || currentPlayer !== 'white') {
        return;
    }
    
    console.log('=== AI开始思考 ===');
    
    // 改进的AI策略：
    // 1. 优先检查是否有能立即获胜的位置
    // 2. 检查是否需要紧急阻止玩家获胜
    // 3. 寻找能形成威胁的位置（能连成4子的位置）
    // 4. 阻止玩家形成威胁
    // 5. 使用评估函数选择最佳位置
    
    let move = null;
    let strategy = '';
    
    if (move = findWinningMove('white')) {
        strategy = '立即获胜';
    } else if (move = findCriticalBlock('black')) {
        strategy = '紧急阻挡';
    } else if (move = findWinningMove('black')) {
        strategy = '阻止对手获胜';
    } else if (move = findDoubleThreatMove('white')) {
        strategy = '创造双威胁';
    } else if (move = findThreatMove('white')) {
        strategy = '创造威胁';
    } else if (move = findThreatMove('black')) {
        strategy = '阻止对手威胁';
    } else if (move = findBestMoveAdvanced()) {
        strategy = '高级评估';
    }
    
    if (move) {
        console.log(`AI决策: ${strategy} - 位置(${move.row}, ${move.col})`);
    }
    
    if (move) {
        const { row, col } = move;
        // 在棋盘上放置白棋
        placePiece(row, col);
        
        // 检查是否获胜
        if (checkWin(row, col)) {
            gameOver = true;
            document.getElementById('game-status').textContent = '白子获胜！';
            return;
        }
        
        // 切换回黑棋回合
        currentPlayer = 'black';
        updateCurrentPlayerDisplay();
    }
}

// 在指定位置放置棋子
function placePiece(row, col) {
    // 更新游戏状态数组
    gameBoard[row][col] = currentPlayer;
    
    // 在页面上显示棋子
    const cell = document.querySelector(`.cell[data-row="${row}"][data-col="${col}"]`);
    const piece = document.createElement('div');
    piece.className = `${currentPlayer}-piece`;
    cell.appendChild(piece);
}

// 更新当前玩家显示
function updateCurrentPlayerDisplay() {
    document.getElementById('current-player').textContent = 
        currentPlayer === 'black' ? '黑子' : '白子';
}

// 检查是否获胜
function checkWin(row, col) {
    // 检查四个方向：水平、垂直、两个对角线
    const directions = [
        [0, 1],  // 水平
        [1, 0],  // 垂直
        [1, 1],  // 对角线 \
        [1, -1]  // 对角线 /
    ];
    
    // 检查每个方向
    for (const [dx, dy] of directions) {
        let count = 1; // 包含当前棋子
        
        // 向一个方向检查
        count += countDirection(row, col, dx, dy);
        
        // 向相反方向检查
        count += countDirection(row, col, -dx, -dy);
        
        // 如果有5个或更多连续的棋子，则获胜
        if (count >= 5) {
            return true;
        }
    }
    
    return false;
}

// 在指定方向上计算连续棋子数量
function countDirection(row, col, dx, dy) {
    let count = 0;
    let r = row + dx;
    let c = col + dy;
    
    // 沿着指定方向继续检查
    while (r >= 0 && r < BOARD_SIZE && c >= 0 && c < BOARD_SIZE &&
           gameBoard[r][c] === currentPlayer) {
        count++;
        r += dx;
        c += dy;
    }
    
    return count;
}

// 紧急阻挡函数 - 专门检测4子连线威胁
function findCriticalBlock(opponentPlayer) {
    // 遍历所有空位，优先检测4子威胁，然后是3子威胁
    for (let minThreat = 4; minThreat >= 3; minThreat--) {
        for (let row = 0; row < BOARD_SIZE; row++) {
            for (let col = 0; col < BOARD_SIZE; col++) {
                if (gameBoard[row][col] === null) {
                    // 检查如果对手在这里下棋是否能连成指定数量的子
                    if (wouldCreateThreat(row, col, opponentPlayer, minThreat)) {
                        console.log(`⚠️ 检测到${minThreat}子威胁！阻挡位置: (${row}, ${col})`);
                        return { row, col };
                    }
                }
            }
        }
    }
    
    return null;
}

// 检查在指定位置放棋是否会创造指定长度的威胁
function wouldCreateThreat(row, col, player, minLength) {
    const directions = [
        [0, 1],  // 水平
        [1, 0],  // 垂直
        [1, 1],  // 对角线 \
        [1, -1]  // 对角线 /
    ];
    
    for (const [dx, dy] of directions) {
        let count = 1; // 包含要放置的棋子
        
        // 向正方向计数
        let r = row + dx;
        let c = col + dy;
        while (r >= 0 && r < BOARD_SIZE && c >= 0 && c < BOARD_SIZE && 
               gameBoard[r][c] === player) {
            count++;
            r += dx;
            c += dy;
        }
        
        // 向负方向计数
        r = row - dx;
        c = col - dy;
        while (r >= 0 && r < BOARD_SIZE && c >= 0 && c < BOARD_SIZE && 
               gameBoard[r][c] === player) {
            count++;
            r -= dx;
            c -= dy;
        }
        
        // 如果连续棋子数达到指定长度，就是威胁
        if (count >= minLength) {
            return true;
        }
    }
    
    return false;
}

// 查找可以获胜的移动
function findWinningMove(player) {
    // 保存当前玩家
    const originalPlayer = currentPlayer;
    
    // 临时设置为指定玩家
    currentPlayer = player;
    
    // 遍历所有空位
    for (let row = 0; row < BOARD_SIZE; row++) {
        for (let col = 0; col < BOARD_SIZE; col++) {
            if (gameBoard[row][col] === null) {
                // 假设在此位置放置棋子
                gameBoard[row][col] = player;
                
                // 检查是否获胜
                if (checkWin(row, col)) {
                    // 恢复原状态
                    gameBoard[row][col] = null;
                    currentPlayer = originalPlayer;
                    return { row, col };
                }
                
                // 恢复原状态
                gameBoard[row][col] = null;
            }
        }
    }
    
    // 恢复原玩家
    currentPlayer = originalPlayer;
    
    return null;
}

// 查找能形成双威胁的移动（一步棋同时在多个方向形成威胁）
function findDoubleThreatMove(player) {
    const originalPlayer = currentPlayer;
    currentPlayer = player;
    
    // 遍历所有空位
    for (let row = 0; row < BOARD_SIZE; row++) {
        for (let col = 0; col < BOARD_SIZE; col++) {
            if (gameBoard[row][col] === null) {
                // 假设在此位置放置棋子
                gameBoard[row][col] = player;
                
                // 计算在多少个方向上形成威胁
                const threatDirections = countThreatDirections(row, col, player);
                
                // 恢复原状态
                gameBoard[row][col] = null;
                
                // 如果能在2个或更多方向形成威胁，这就是双威胁
                if (threatDirections >= 2) {
                    currentPlayer = originalPlayer;
                    return { row, col };
                }
            }
        }
    }
    
    currentPlayer = originalPlayer;
    return null;
}

// 计算在多少个方向上形成威胁
function countThreatDirections(row, col, player) {
    const directions = [
        [0, 1],  // 水平
        [1, 0],  // 垂直
        [1, 1],  // 对角线 \
        [1, -1]  // 对角线 /
    ];
    
    let threatDirections = 0;
    
    for (const [dx, dy] of directions) {
        const count = 1 + countDirection(row, col, dx, dy) + countDirection(row, col, -dx, -dy);
        
        if (count >= 3) { // 连成3子或更多就算威胁
            const openEnds = countOpenEnds(row, col, dx, dy, player);
            if (openEnds >= 1) { // 至少有一个开口
                threatDirections++;
            }
        }
    }
    
    return threatDirections;
}

// 查找能形成威胁的移动（连成4子，有两个开口的3子等）
function findThreatMove(player) {
    const originalPlayer = currentPlayer;
    currentPlayer = player;
    
    let bestThreat = null;
    let maxThreatValue = 0;
    
    // 遍历所有空位
    for (let row = 0; row < BOARD_SIZE; row++) {
        for (let col = 0; col < BOARD_SIZE; col++) {
            if (gameBoard[row][col] === null) {
                // 假设在此位置放置棋子
                gameBoard[row][col] = player;
                
                // 评估威胁值
                const threatValue = evaluateThreat(row, col, player);
                
                if (threatValue > maxThreatValue) {
                    maxThreatValue = threatValue;
                    bestThreat = { row, col };
                }
                
                // 恢复原状态
                gameBoard[row][col] = null;
            }
        }
    }
    
    currentPlayer = originalPlayer;
    
    // 只有威胁值达到一定程度才返回（降低阈值，更敏感）
    return maxThreatValue >= 50 ? bestThreat : null;
}

// 评估威胁值
function evaluateThreat(row, col, player) {
    const directions = [
        [0, 1],  // 水平
        [1, 0],  // 垂直
        [1, 1],  // 对角线 \
        [1, -1]  // 对角线 /
    ];
    
    let maxThreat = 0;
    
    for (const [dx, dy] of directions) {
        const count = 1 + countDirection(row, col, dx, dy) + countDirection(row, col, -dx, -dy);
        
        if (count >= 4) {
            maxThreat = Math.max(maxThreat, 1000); // 能连成4子或更多
        } else if (count === 3) {
            // 检查是否有开口
            const openEnds = countOpenEnds(row, col, dx, dy, player);
            if (openEnds >= 1) {
                maxThreat = Math.max(maxThreat, 300); // 活三
            } else {
                maxThreat = Math.max(maxThreat, 100); // 冲三
            }
        } else if (count === 2) {
            const openEnds = countOpenEnds(row, col, dx, dy, player);
            if (openEnds >= 2) {
                maxThreat = Math.max(maxThreat, 50); // 活二
            }
        }
    }
    
    return maxThreat;
}

// 计算开口数量
function countOpenEnds(row, col, dx, dy, player) {
    let openEnds = 0;
    
    // 检查正方向的开口
    let r = row + dx;
    let c = col + dy;
    
    // 跳过同色棋子
    while (r >= 0 && r < BOARD_SIZE && c >= 0 && c < BOARD_SIZE && gameBoard[r][c] === player) {
        r += dx;
        c += dy;
    }
    
    // 检查是否是空位（开口）
    if (r >= 0 && r < BOARD_SIZE && c >= 0 && c < BOARD_SIZE && gameBoard[r][c] === null) {
        openEnds++;
    }
    
    // 检查负方向的开口
    r = row - dx;
    c = col - dy;
    
    // 跳过同色棋子
    while (r >= 0 && r < BOARD_SIZE && c >= 0 && c < BOARD_SIZE && gameBoard[r][c] === player) {
        r -= dx;
        c -= dy;
    }
    
    // 检查是否是空位（开口）
    if (r >= 0 && r < BOARD_SIZE && c >= 0 && c < BOARD_SIZE && gameBoard[r][c] === null) {
        openEnds++;
    }
    
    return openEnds;
}

// 高级评估函数
function findBestMoveAdvanced() {
    let bestMove = null;
    let maxScore = -Infinity;
    
    // 遍历所有空位
    for (let row = 0; row < BOARD_SIZE; row++) {
        for (let col = 0; col < BOARD_SIZE; col++) {
            if (gameBoard[row][col] === null) {
                const score = evaluatePosition(row, col);
                
                if (score > maxScore) {
                    maxScore = score;
                    bestMove = { row, col };
                }
            }
        }
    }
    
    return bestMove;
}

// 评估位置得分
function evaluatePosition(row, col) {
    let score = 0;
    
    // 位置价值：中心位置更有价值，关键交叉点额外加分
    const center = Math.floor(BOARD_SIZE / 2);
    const distanceFromCenter = Math.abs(row - center) + Math.abs(col - center);
    score += (BOARD_SIZE - distanceFromCenter) * 15;
    
    // 中心点特别重要
    if (row === center && col === center) {
        score += 50;
    }
    
    // 模拟白棋在此位置的威胁值
    gameBoard[row][col] = 'white';
    const whiteCurrentPlayer = currentPlayer;
    currentPlayer = 'white';
    const whiteThreat = evaluateThreat(row, col, 'white');
    const whiteThreatDirs = countThreatDirections(row, col, 'white');
    currentPlayer = whiteCurrentPlayer;
    gameBoard[row][col] = null;
    
    // 模拟黑棋在此位置的威胁值（防守）
    gameBoard[row][col] = 'black';
    currentPlayer = 'black';
    const blackThreat = evaluateThreat(row, col, 'black');
    const blackThreatDirs = countThreatDirections(row, col, 'black');
    currentPlayer = whiteCurrentPlayer;
    gameBoard[row][col] = null;
    
    // 攻击和防守的平衡，多方向威胁额外加分
    score += whiteThreat * 1.3; // 攻击优先
    score += whiteThreatDirs * 100; // 多方向威胁奖励
    score += blackThreat * 1.1; // 防守
    score += blackThreatDirs * 80; // 阻止对手多方向威胁
    
    // 连接性评估：靠近已有棋子的位置更有价值
    score += evaluateConnectivity(row, col);
    
    // 边角位置适当减分（除非有特殊战术意义）
    if ((row === 0 || row === BOARD_SIZE - 1) && (col === 0 || col === BOARD_SIZE - 1)) {
        score -= 20;
    }
    
    return score;
}

// 评估连接性
function evaluateConnectivity(row, col) {
    let connectivity = 0;
    
    // 检查周围8个方向
    for (let dr = -1; dr <= 1; dr++) {
        for (let dc = -1; dc <= 1; dc++) {
            if (dr === 0 && dc === 0) continue;
            
            const newRow = row + dr;
            const newCol = col + dc;
            
            if (newRow >= 0 && newRow < BOARD_SIZE && newCol >= 0 && newCol < BOARD_SIZE) {
                if (gameBoard[newRow][newCol] === 'white') {
                    connectivity += 20; // 靠近自己的棋子
                } else if (gameBoard[newRow][newCol] === 'black') {
                    connectivity += 15; // 靠近对手棋子也有价值（可以干扰）
                }
            }
        }
    }
    
    return connectivity;
}

// 保留原有的简单查找最佳移动函数作为后备
function findBestMove() {
    // 策略：优先选择靠近黑棋的空位
    
    // 首先查找是否有相邻的空位
    for (let row = 0; row < BOARD_SIZE; row++) {
        for (let col = 0; col < BOARD_SIZE; col++) {
            if (gameBoard[row][col] === 'black') {
                // 检查周围的空位
                for (let dr = -1; dr <= 1; dr++) {
                    for (let dc = -1; dc <= 1; dc++) {
                        if (dr === 0 && dc === 0) continue; // 跳过当前位置
                        
                        const newRow = row + dr;
                        const newCol = col + dc;
                        
                        // 检查边界
                        if (newRow >= 0 && newRow < BOARD_SIZE &&
                            newCol >= 0 && newCol < BOARD_SIZE &&
                            gameBoard[newRow][newCol] === null) {
                            return { row: newRow, col: newCol };
                        }
                    }
                }
            }
        }
    }
    
    // 如果没有相邻的空位，则选择中心位置
    const center = Math.floor(BOARD_SIZE / 2);
    if (gameBoard[center][center] === null) {
        return { row: center, col: center };
    }
    
    // 查找离中心最近的空位
    let bestMove = null;
    let minDistance = Infinity;
    
    for (let row = 0; row < BOARD_SIZE; row++) {
        for (let col = 0; col < BOARD_SIZE; col++) {
            if (gameBoard[row][col] === null) {
                // 计算到中心的距离
                const distance = Math.abs(row - center) + Math.abs(col - center);
                if (distance < minDistance) {
                    minDistance = distance;
                    bestMove = { row, col };
                }
            }
        }
    }
    
    return bestMove;
}

// 重新开始游戏
function restartGame() {
    initGame();
}

// 页面加载完成后初始化游戏
document.addEventListener('DOMContentLoaded', () => {
    // 绑定重新开始按钮事件
    document.getElementById('restart-btn').addEventListener('click', restartGame);
    
    // 初始化游戏
    initGame();
});